﻿using Amazon.DynamoDBv2.DataModel;
using Amazon.Runtime.Internal;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using platform.Components.Core;
using platform.Constants;
using platform.Models.Request;
using platform.Models.Request.AgentKnowledgebase;
using platform.Models.Response;
using shared.Components.ApiEventBus;
using shared.Components.MessageBus.Interfaces;
using shared.Components.MessageBus.Models;
using shared.Components.MultiStepProcess;
using shared.Components.MultiStepProcess.Enums;
using shared.Components.MultiStepProcess.Interfaces;
using shared.Components.MultiStepProcess.Models;
using shared.Controllers;
using shared.Models.Documents.DynamoDB;
using shared.Models.Documents.DynamoDB.ProviderData;
using shared.Models.Enums;
using shared.Services;

namespace platform.Controllers
{
    [Route(Routes.AgentKnowledgebaseController.BasePath)]
    [Produces("application/json")]
    public class AgentKnowledgebaseController : SuperController2
    {

        private readonly Amazon.BedrockAgent.IAmazonBedrockAgent bedrockAgent;

        public AgentKnowledgebaseController(IDynamoDBContext dBContext, Amazon.BedrockAgent.IAmazonBedrockAgent bedrockAgent, IMessageBus messageBus, IUniqueIDService uniqueIDService) : base(dBContext, messageBus, uniqueIDService)
        {
            this.bedrockAgent = bedrockAgent;
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Routes.AgentKnowledgebaseController.Public.ASSOC_EXISTS)]
        public async Task<IActionResult> Exists([FromQuery] AgentKnowledgebaseAssociationExistsRequest request)
        {

            if (!ModelState.IsValid) return BadRequest(ModelState);

            string accountId = GetAccountId();
            var akb = await GetDBEntry2<shared.Models.Documents.DynamoDB.AgentKnowledgeBase>(request.KbId, request.AgentId, AgentKnowledgeBase.KnowledgebaseIdAgentIdHashIndex);
            if (akb == null) return Ok(new AgentKnowledgebaseExistsResponse() { Exists = false});

            return Ok(new AgentKnowledgebaseExistsResponse() { Exists = (akb.AccountId == accountId) });
        }


        #region ASSIGN PROCESS
        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> AssignProcessAssignAWS(AgentKnowledgeBase agentKb)
        {
            var accountId = GetAccountId();
            var knowledgeBase = await GetDBEntry2<AwsKnowledgeBase>(accountId, agentKb.KnowledgebaseId);
            var agent = await GetDBEntry2<AwsAgent>(accountId, agentKb.AgentId);

            var awsRequest = new Amazon.BedrockAgent.Model.AssociateAgentKnowledgeBaseRequest();
            awsRequest.AgentId = agent.AwsData.AgentId;
            awsRequest.KnowledgeBaseId = knowledgeBase.AwsData.AwsKbId;
            awsRequest.Description = agentKb.Description;
            awsRequest.KnowledgeBaseState = "ENABLED";
            awsRequest.AgentVersion = "DRAFT";

            try
            {
                var resp = await bedrockAgent.AssociateAgentKnowledgeBaseAsync(awsRequest);
                return await Task.FromResult((MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult() { ShouldUpdateDb = true }));
            }
            catch (Exception ex)
            {
                return await Task.FromResult((MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 20 }));
            }

        }

        private async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> AssignProcessUnassignAWS(AgentKnowledgeBase agentKb)
        {
            var awsRequest = new Amazon.BedrockAgent.Model.DisassociateAgentKnowledgeBaseRequest
            {
                AgentId = agentKb.AgentId,
                KnowledgeBaseId = agentKb.KnowledgebaseId,
                AgentVersion = "DRAFT"
            };

            try
            {
                var resp = await bedrockAgent.DisassociateAgentKnowledgeBaseAsync(awsRequest);
                return await Task.FromResult((MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult() { ShouldUpdateDb = true }));
            }
            catch (Exception ex)
            {
                return await Task.FromResult((MultiStepProcessTaskRunResult.FailedRetry, new SuperControllerMultiStepResult() { DelayInSeconds = 20 }));
            }

        }

        private IMultiStepProcess<AgentKnowledgeBaseStatus, AgentKnowledgeBase, SuperControllerMultiStepResult> GetAssignProcess()
        {
            var process = new MultiStepProcess<AgentKnowledgeBaseStatus, AgentKnowledgeBase, SuperControllerMultiStepResult>()
                .DefineTransition(
                    AgentKnowledgeBaseStatus.ASSIGNING,
                    AgentKnowledgeBaseStatus.READY,
                    new MultiStepProcessTransitionDetails<AgentKnowledgeBase, SuperControllerMultiStepResult>(AssignProcessAssignAWS, maxRetries: 10),
                    new MultiStepProcessTransitionDetails<AgentKnowledgeBase, SuperControllerMultiStepResult>(AssignProcessUnassignAWS, maxRetries: 10),
                    "Assign Entry"
                );

            return process;
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost(Constants.Routes.AgentKnowledgebaseController.Public.ASSIGN)]
        public async Task<IActionResult> AssignAgentKnowledgeBase([FromBody] AgentAssignKbRequest request)
        {

            if (!ModelState.IsValid) return BadRequest(ModelState);

            string accountId = GetAccountId();

            var akb = await GetDBEntry2<shared.Models.Documents.DynamoDB.AgentKnowledgeBase>(request.kbId, request.AgentId, AgentKnowledgeBase.KnowledgebaseIdAgentIdHashIndex);
            if (akb != null && akb.AccountId == accountId) return BadRequest("This association already exists.");

            var knowledgeBase = await GetDBEntry<AwsKnowledgeBase>(accountId, request.kbId);
            var agent = await GetDBEntry<AwsAgent>(accountId, request.AgentId);

            if (knowledgeBase == null || agent == null)
            {
                return BadRequest("Invalid Agent or Knowledgebase.");
            }

            var newAgentKb = new AgentKnowledgeBase() { 
                AccountId = accountId,
                KnowledgebaseId = request.kbId,
                AgentId = request.AgentId,
                Status = AgentKnowledgeBaseStatus.ASSIGNING,
                Description = request.Description,
            };


            var process = GetAssignProcess();
            process.Initialize(newAgentKb);

            MessageBusDispachParams messageBusDispachParams = new MessageBusDispachParams()
            {
                Controller = Routes.AgentKnowledgebaseController.BasePath,
                Method = HttpMethod.Post,
                DelayInSeconds = 0,
                Route = Constants.Routes.AgentKnowledgebaseController.Internal.ASSIGN,
                TargetMicroservice = MicroserviceType.Platform,
                Payload = process.StateData
            };

            await this.DispatchApiEventV2(messageBusDispachParams);

            await PutDBEntry2<AgentKnowledgeBase>(newAgentKb);

            return Ok(newAgentKb);

        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.MicroserviceAuthScheme)]
        [Route(Constants.Routes.AgentKnowledgebaseController.Internal.ASSIGN)]
        [HttpPost]
        public async Task<IActionResult> CreateProcess([FromBody] MessageBusMessage message)
        {
            return await MultiStepProcess(message, GetAssignProcess());
        }
        #endregion



        
    }
}
