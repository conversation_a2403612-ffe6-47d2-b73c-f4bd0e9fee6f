using Microsoft.AspNetCore.Mvc;
using shared.Controllers;
using shared.Models.Documents.DynamoDB;
using shared.Models.Response;
using Amazon.DynamoDBv2.DataModel;
using shared.Components.MessageBus.Interfaces;
using shared.Services;

namespace shared.Examples
{
    /// <summary>
    /// Example demonstrating Range Key join functionality
    /// Shows how the GetDBJoin method automatically optimizes joins based on key structure
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class RangeKeyJoinExampleController : SuperController2
    {
        public RangeKeyJoinExampleController(IDynamoDBContext dBContext, IMessageBus messageBus, IUniqueIDService uniqueIDService)
            : base(dBContext, messageBus, uniqueIDService)
        {
        }

        /// <summary>
        /// Example of Range Key join - automatically uses KeyExpression for better performance
        /// Joins AgentKnowledgeBase (Range Key: KnowledgebaseId) with KnowledgeBase (Range Key: KbId)
        /// </summary>
        [HttpGet("range-key-join/{agentId}")]
        public async Task<IActionResult> RangeKeyJoinExample(
            string agentId,
            [FromQuery] string? nextToken = null,
            [FromQuery] int count = 10)
        {
            try
            {
                string accountId = GetAccountId();

                // This join will automatically use KeyExpression because:
                // - KnowledgebaseId is a Range Key in AgentKnowledgeBase table
                // - KbId is a Range Key in KnowledgeBase table
                var joinConfig = new DBJoinConfig<AgentKnowledgeBase, KnowledgeBase>
                {
                    FirstTable = new TableQueryConfig
                    {
                        HashKeyValue = agentId,
                        Index = null, // Primary table
                        AdditionalFilterExpression = new Amazon.DynamoDBv2.DocumentModel.Expression()
                        {
                            ExpressionAttributeNames = new Dictionary<string, string>
                            {
                                { $"#{nameof(AgentKnowledgeBase.AccountId)}", nameof(AgentKnowledgeBase.AccountId) }
                            },
                            ExpressionAttributeValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry>
                            {
                                { $":{nameof(AgentKnowledgeBase.AccountId)}", accountId }
                            },
                            ExpressionStatement = $"#{nameof(AgentKnowledgeBase.AccountId)}=:{nameof(AgentKnowledgeBase.AccountId)}"
                        }
                    },
                    SecondTable = new TableQueryConfig
                    {
                        HashKeyValue = accountId,
                        Index = null // Primary table
                    },
                    JoinCondition = new JoinCondition
                    {
                        // Both of these are Range Keys, so KeyExpression will be used automatically
                        FirstTableField = nameof(AgentKnowledgeBase.KnowledgebaseId),
                        SecondTableField = nameof(KnowledgeBase.KbId)
                    },
                    TargetResultCount = count
                };

                var result = await GetDBJoin(joinConfig, nextToken);

                var response = new ListResponse<KnowledgeBase>
                {
                    Entries = result.Entries.Select(entry => entry.Second).ToList(),
                    NextToken = result.NextToken,
                    Total = result.Total
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest($"Error performing range key join: {ex.Message}");
            }
        }

        /// <summary>
        /// Example of non-key field join - automatically uses FilterExpression
        /// Demonstrates joining on non-key fields for comparison
        /// </summary>
        [HttpGet("filter-join/{accountId}")]
        public async Task<IActionResult> FilterJoinExample(
            string accountId,
            [FromQuery] string? status = null,
            [FromQuery] string? nextToken = null,
            [FromQuery] int count = 10)
        {
            try
            {
                // This would be a hypothetical join on Status fields (non-key fields)
                // The method would automatically use FilterExpression
                
                // Note: This is a conceptual example - actual implementation would depend on
                // having tables with Status fields that can be joined
                
                return Ok(new { 
                    Message = "This demonstrates how non-key field joins would work",
                    Note = "The GetDBJoin method automatically detects field types and uses appropriate expressions",
                    RangeKeyJoin = "Uses KeyExpression for better performance",
                    NonKeyJoin = "Uses FilterExpression for attribute-based filtering"
                });
            }
            catch (Exception ex)
            {
                return BadRequest($"Error performing filter join: {ex.Message}");
            }
        }

        /// <summary>
        /// Example showing how to use GetDBEntries2 with additional KeyExpression directly
        /// This demonstrates the enhanced functionality at a lower level
        /// </summary>
        [HttpGet("enhanced-query/{accountId}")]
        public async Task<IActionResult> EnhancedQueryExample(
            string accountId,
            [FromQuery] string? kbId = null,
            [FromQuery] string? nextToken = null,
            [FromQuery] int count = 10)
        {
            try
            {
                Amazon.DynamoDBv2.DocumentModel.Expression? additionalKeyExpression = null;

                // If kbId is provided, create an additional key expression for the range key
                if (!string.IsNullOrEmpty(kbId))
                {
                    additionalKeyExpression = new Amazon.DynamoDBv2.DocumentModel.Expression()
                    {
                        ExpressionAttributeNames = new Dictionary<string, string>
                        {
                            { "#KbId", nameof(KnowledgeBase.KbId) }
                        },
                        ExpressionAttributeValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry>
                        {
                            { ":kbId", kbId }
                        },
                        ExpressionStatement = "#KbId = :kbId"
                    };
                }

                // Use the enhanced GetDBEntries2 method with additional key expression
                var result = await GetDBEntries2<KnowledgeBase>(
                    accountId,
                    count,
                    nextToken,
                    index: null,
                    attributesToGet: null,
                    filterExpression: null,
                    skipCount: false,
                    additionalKeyExpression: additionalKeyExpression
                );

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest($"Error performing enhanced query: {ex.Message}");
            }
        }
    }
}
