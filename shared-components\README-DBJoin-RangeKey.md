# DynamoDB Join Operations - Range Key Support

## Overview

The GetDBJoin method now intelligently handles join conditions on Range Keys by using KeyExpression instead of FilterExpression for better performance and correctness.

## Key Improvements

### 1. Enhanced GetDBEntries2 Method
- Added `additionalKeyExpression` parameter
- Automatically combines hash key expression with additional key expressions using AND logic
- Maintains backward compatibility with existing code

### 2. Smart Join Condition Detection
The GetDBJoin method now:
- Automatically detects if the join condition is on a Range Key
- Uses KeyExpression for Range Key joins (better performance)
- Uses FilterExpression for non-key field joins (existing behavior)

## Technical Details

### Range Key Detection
```csharp
// The method automatically checks if the join field is a Range Key
if (IsRangeKey<T2>(config.JoinCondition.SecondTableField, config.SecondTable.Index))
{
    // Use KeyExpression for Range Key joins
    additionalKeyExpression = BuildInKeyExpression(config.JoinCondition.SecondTableField, joinValues);
}
else
{
    // Use FilterExpression for non-key field joins
    filterExpression = BuildInFilterExpression(config.JoinCondition.SecondTableField, joinValues);
}
```

### KeyExpression vs FilterExpression

**KeyExpression (Range Key joins):**
- More efficient as it uses the table's key structure
- Better performance for large datasets
- Automatically used when joining on Range Keys

**FilterExpression (Non-key field joins):**
- Used for fields that are not part of the key structure
- Maintains existing functionality for attribute-based joins

## Example Usage

### Range Key Join Example
```csharp
// Joining on a Range Key (e.g., KnowledgebaseId in AgentKnowledgeBase table)
var joinConfig = new DBJoinConfig<AgentKnowledgeBase, KnowledgeBase>
{
    FirstTable = new TableQueryConfig
    {
        HashKeyValue = agentId,
        Index = null
    },
    SecondTable = new TableQueryConfig
    {
        HashKeyValue = accountId,
        Index = null
    },
    JoinCondition = new JoinCondition
    {
        FirstTableField = nameof(AgentKnowledgeBase.KnowledgebaseId), // Range Key
        SecondTableField = nameof(KnowledgeBase.KbId) // Range Key
    }
};

// The method automatically detects this is a Range Key join and uses KeyExpression
var result = await GetDBJoin(joinConfig, nextToken);
```

### Non-Key Field Join Example
```csharp
// Joining on a non-key field (e.g., Status)
var joinConfig = new DBJoinConfig<Table1, Table2>
{
    JoinCondition = new JoinCondition
    {
        FirstTableField = "Status", // Non-key field
        SecondTableField = "Status" // Non-key field
    }
};

// The method automatically uses FilterExpression for this join
var result = await GetDBJoin(joinConfig, nextToken);
```

## Performance Benefits

1. **Range Key Joins**: Use DynamoDB's native key querying capabilities
2. **Automatic Optimization**: No manual configuration required
3. **Backward Compatibility**: Existing code continues to work unchanged
4. **Efficient Pagination**: Proper token management for both key and filter-based queries

## Migration Notes

- **No code changes required** for existing implementations
- **Automatic optimization** for Range Key joins
- **Improved performance** for joins on key fields
- **Maintains compatibility** with all existing filter expressions

The implementation automatically chooses the most efficient query method based on the join condition, providing optimal performance without requiring changes to existing code.
