﻿using Amazon.DynamoDBv2.DataModel;
using Amazon.DynamoDBv2.DocumentModel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using shared.Components.ApiEventBus;
using shared.Components.MessageBus.Interfaces;
using shared.Components.MessageBus.Models;
using shared.Components.MultiStepProcess.Enums;
using shared.Components.MultiStepProcess.Interfaces;
using shared.Components.MultiStepProcess.Models;
using shared.Converters;
using shared.Models.Documents;
using shared.Models.Documents.DynamoDB;
using shared.Models.Enums;
using shared.Models.Interfaces;
using shared.Models.Response;
using shared.Services;
using Stateless;
using System.Security.Claims;
using System.Text;
using System.Text.Json;

namespace shared.Controllers
{
    public class SuperController2 : Controller
    {
        protected readonly IMessageBus messageBus;
        protected readonly IDynamoDBContext dBContext;
        protected readonly IUniqueIDService uniqueIDService;

        public SuperController2(IDynamoDBContext dBContext, IMessageBus messageBus, IUniqueIDService uniqueIDService)
        {
            this.uniqueIDService = uniqueIDService;
            this.dBContext = dBContext;
            this.messageBus = messageBus;
        }


        #region MESSAGE BUS
            protected async Task<MessageBusResult> DispatchApiEventV2(MessageBusDispachParams dispatch)
            {
                List<Claim> claims = new List<Claim> { new Claim("AccountId", GetAccountId()) };

                try
                {
                    var request = new MessageBusSendRequest<object>(dispatch.Payload, dispatch.TargetMicroservice, dispatch.Controller, dispatch.Route);
                    request.DelayInSeconds = dispatch.DelayInSeconds;
                    request.Claims = claims;

                    return await messageBus.SendAsync(request);
                }
                catch (Exception ex)
                {
                    return MessageBusResult.Error;
                }
            }

            protected async Task<MessageBusResult> RequeueV2(MessageBusMessage message)
            {
                return await messageBus.Requeue(message);
            }
            #endregion


        #region MULTI STEP PROCESS
            protected class SuperControllerMultiStepResult
            {
                public bool ShouldUpdateDb { get; set; } = true;
                public int DelayInSeconds { get; set; } = 3;
                public bool IsAtomicStatusUpdate { get; set; } = false;
            }

            protected async Task<(MultiStepProcessTaskRunResult, SuperControllerMultiStepResult?)> MultiStepProcessDummyTask(object any)
            {
                return await Task.FromResult((MultiStepProcessTaskRunResult.Successful, new SuperControllerMultiStepResult()));
            }

        protected async Task<IActionResult> MultiStepProcess<TState, TObject>(MessageBusMessage message, IMultiStepProcess<TState, TObject, SuperControllerMultiStepResult> process) where TState : struct where TObject : DynamoDBModel, IStateful<TState>, INoSQLStatic
        {
            var stateData = message.GetPayload<MultiStepProcessStateData<TState, TObject>>();
            if (stateData == null)
            {
                return BadRequest();
            }
            process.LoadStateData(stateData);
            TState previousState = process.CurrentState;

            bool shouldUpdateDb = true;
            MessageBusDispachParams nextDispatch = new MessageBusDispachParams(message);
            nextDispatch.DelayInSeconds = 10 * message.RetryCount;

            bool shouldRequeue = false;

            //missing a save only
            if (!process.IsFinished)
            {
                var iterationResult = await process.ExecuteIteration();

                if (iterationResult.Result != null)
                {
                    nextDispatch.DelayInSeconds = iterationResult.Result.DelayInSeconds;
                    shouldUpdateDb = iterationResult.Result.ShouldUpdateDb;
                    if (iterationResult.Result.IsAtomicStatusUpdate)
                    {
                        if (!await SetDBEntryStatusAtomic<TObject, TState>(stateData.StatefulObject, process.CurrentState, new List<TState>() { previousState }))
                        {
                            return BadRequest();
                        }
                    }
                }


                if (!iterationResult.Success)
                {
                    switch (process.FailureReason)
                    {
                        case Components.MultiStepProcess.Enums.MultiStepProcessFailureReason.Timeout:
                            shouldRequeue = true;
                            break;
                        case Components.MultiStepProcess.Enums.MultiStepProcessFailureReason.RollbackFailed:
                        case Components.MultiStepProcess.Enums.MultiStepProcessFailureReason.RetryLimitExceeded:
                        case Components.MultiStepProcess.Enums.MultiStepProcessFailureReason.TaskAborted:
                        case Components.MultiStepProcess.Enums.MultiStepProcessFailureReason.InvalidTransition:
                        case Components.MultiStepProcess.Enums.MultiStepProcessFailureReason.InvalidConfiguration:
                            return StatusCode(500);
                    }

                }
            }



            bool updatedDb = true;
            if (shouldUpdateDb) updatedDb = await PutDBEntry2(stateData.StatefulObject);

            if (process.IsFinished && !updatedDb && shouldUpdateDb && nextDispatch != null)
            {
                await DispatchApiEventV2(nextDispatch);
            }
            else if (!process.IsFinished && nextDispatch != null)
            {
                await DispatchApiEventV2(nextDispatch);
            }
            else if (shouldRequeue)
            {
                await RequeueV2(message);
            }

            switch (await messageBus.ConfirmMessage(message))
            {
                case MessageBusResult.Error:
                case MessageBusResult.TooManyRetries:
                    Console.WriteLine("Failed to confirm message to the bus, will duplicate");
                    break;
            }

            return Ok();

        }



        protected async Task<IActionResult> WrappedMultiStepProcess<TState, TObject>(MessageBusMessage message, IMultiStepProcess<TState, IMultiStepProcessWrapper<TObject, TState>, SuperControllerMultiStepResult> process) where TState : struct where TObject : DynamoDBModel, IStateful<TState>, INoSQLStatic
        {
            var stateData = message.GetPayload<MultiStepProcessStateData<TState, IMultiStepProcessWrapper<TObject, TState>>>();
            if (stateData == null)
            {
                return BadRequest();
            }
            process.LoadStateData(stateData);
            TState previousState = process.CurrentState;

            bool shouldUpdateDb = true;
            MessageBusDispachParams nextDispatch = new MessageBusDispachParams(message);
            nextDispatch.DelayInSeconds = 10 * message.RetryCount;

            bool shouldRequeue = false;

            //missing a save only
            if (!process.IsFinished)
            {
                var iterationResult = await process.ExecuteIteration();

                if (iterationResult.Result != null)
                {
                    nextDispatch.DelayInSeconds = iterationResult.Result.DelayInSeconds;
                    shouldUpdateDb = iterationResult.Result.ShouldUpdateDb;
                    if (iterationResult.Result.IsAtomicStatusUpdate)
                    {
                        if (!await SetDBEntryStatusAtomic<TObject, TState>(stateData.StatefulObject.GetObject(), process.CurrentState, new List<TState>() { previousState }))
                        {
                            return BadRequest();
                        }
                    }
                }


                if (!iterationResult.Success)
                {
                    switch (process.FailureReason)
                    {
                        case Components.MultiStepProcess.Enums.MultiStepProcessFailureReason.Timeout:
                            shouldRequeue = true;
                            break;
                        case Components.MultiStepProcess.Enums.MultiStepProcessFailureReason.RollbackFailed:
                        case Components.MultiStepProcess.Enums.MultiStepProcessFailureReason.RetryLimitExceeded:
                        case Components.MultiStepProcess.Enums.MultiStepProcessFailureReason.TaskAborted:
                        case Components.MultiStepProcess.Enums.MultiStepProcessFailureReason.InvalidTransition:
                        case Components.MultiStepProcess.Enums.MultiStepProcessFailureReason.InvalidConfiguration:
                            return StatusCode(500);
                    }

                }
            }



            bool updatedDb = true;
            if (shouldUpdateDb) updatedDb = await PutDBEntry2(stateData.StatefulObject.GetObject());

            if (process.IsFinished && !updatedDb && shouldUpdateDb && nextDispatch != null)
            {
                await DispatchApiEventV2(nextDispatch);
            }
            else if (!process.IsFinished && nextDispatch != null)
            {
                await DispatchApiEventV2(nextDispatch);
            }
            else if (shouldRequeue)
            {
                await RequeueV2(message);
            }

            switch (await messageBus.ConfirmMessage(message))
            {
                case MessageBusResult.Error:
                case MessageBusResult.TooManyRetries:
                    Console.WriteLine("Failed to confirm message to the bus, will duplicate");
                    break;
            }

            return Ok();

        }

        #endregion


        #region NoSQL Operations
        protected async Task<ListResponse<T>> GetDBEntries2<T>(
            string hashKeyValue,
            int limit,
            string? previousToken = null,
            string? index = null,
            List<string>? attributesToGet = null,
            Amazon.DynamoDBv2.DocumentModel.Expression? filterExpression = null,
            bool skipCount = false,
            Amazon.DynamoDBv2.DocumentModel.Expression? additionalKeyExpression = null
            ) where T : INoSQLStatic
        {
            string hashKeyName = T.GetHashKeyPropertyName(index);
            int totalElements = -1;

            // Build the key expression, starting with the hash key
            var keyExpression = new Amazon.DynamoDBv2.DocumentModel.Expression()
            {
                ExpressionAttributeNames = new Dictionary<string, string>() { { $"#{hashKeyName}", hashKeyName } },
                ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>() { { $":{hashKeyName}", hashKeyValue } },
                ExpressionStatement = $"#{hashKeyName}=:{hashKeyName}"
            };

            // Combine with additional key expression if provided
            if (additionalKeyExpression != null)
            {
                keyExpression = CombineKeyExpressions(keyExpression, additionalKeyExpression);
            }

            var config = new QueryOperationConfig()
            {
                Limit = limit,
                KeyExpression = keyExpression,
                FilterExpression = filterExpression,
                BackwardSearch = true
            };

            if (index != null) config.IndexName = index;

            var table = dBContext.GetTargetTable<T>();

            if (previousToken == null && !skipCount) {

                var countConfig = new QueryOperationConfig()
                {
                    Limit = 1,
                    Select = SelectValues.Count,
                    KeyExpression = keyExpression, // Use the same combined key expression
                    FilterExpression = filterExpression,
                };
                if (index != null) countConfig.IndexName = index;

                var countSearch = table.Query(countConfig);
                await countSearch.GetRemainingAsync();

                if (countSearch.Count <= 0)
                {
                    return new ListResponse<T>() { Entries = new List<T>(), NextToken = null, Total = totalElements };
                }

                totalElements = countSearch.Count;
            }
            else
            {
                config.PaginationToken = previousToken;
            }


            if (attributesToGet != null)
            {
                config.AttributesToGet = attributesToGet;
                config.Select = SelectValues.SpecificAttributes;
            }
            else
            {
                config.Select = SelectValues.AllAttributes;
            }

            var search = dBContext.FromQueryAsync<T>(config);
            List<T> entries = new List<T>();
            entries.AddRange(await search.GetNextSetAsync());
            return new ListResponse<T>() { Entries = entries, NextToken = search.PaginationToken, Total = totalElements };
        }


        protected async Task<T?> GetDBEntry2<T>(string hashKey, string? rangeKey = null, string? index = null) where T : DynamoDBModel, INoSQLStatic
        {

            if(index == null)
            {
                if(rangeKey == null)
                {
                    return await dBContext.LoadAsync<T>(hashKey);
                }
                else
                {
                    return await dBContext.LoadAsync<T>(hashKey, rangeKey);
                }
            }
            else
            {
                if(rangeKey == null) throw new ArgumentNullException(nameof(rangeKey));
                var rangekeyPropertyName = T.GetRangeKeyPropertyName(index);
                var queryConfig = new QueryConfig()
                {
                    IndexName = index,
                    QueryFilter = new List<ScanCondition>() {
                            {
                                new ScanCondition(
                                    rangekeyPropertyName,
                                    ScanOperator.Equal,
                                    [rangeKey]
                                )
                            }
                    }
                };
                var search = dBContext.QueryAsync<T>(hashKey, queryConfig);
                var entities = await search.GetRemainingAsync();
                if (entities.Count != 1) return null;
                return entities[0];
            }

        }

        protected async Task<bool> PutDBEntry2<T>(T entity) where T : DynamoDBModel
        {
            try
            {
                entity.DataVersion += 1;
                entity.LastChangeTimestamp = CurrentTimestamp();
                entity.SearchString = entity.GetSearchString();
                await dBContext.SaveAsync(entity);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        protected async Task<bool> DeleteDBEntry2<T>(T item) where T : DynamoDBModel
        {
            try
            {
                var hashKeyValue = item.GetHashKeyValue();
                if (string.IsNullOrEmpty(hashKeyValue))
                {
                    return false;
                }

                var rangeKeyValue = item.GetRangeKeyValue();

                if (!string.IsNullOrEmpty(rangeKeyValue))
                {
                    // Model has both hash and range key
                    await dBContext.DeleteAsync<T>(hashKeyValue, rangeKeyValue);
                }
                else
                {
                    // Model has only hash key
                    await dBContext.DeleteAsync<T>(hashKeyValue);
                }

                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }


        protected async Task<bool> UpdateDBEntry2<T>(
            T item,
            List<string> fields,
            bool atomic = true) where T : DynamoDBModel
        {

            var conditionExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            if (atomic)
            {
                conditionExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#D", "DataVersion" } };
                conditionExpression.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>() { { ":d", item.DataVersion } };
                conditionExpression.ExpressionStatement = "#D=:d";
            }

            var expressionAttributeNames = new Dictionary<string, string>() { { "#D", nameof(DynamoDBModel.DataVersion) }, { "#T", nameof(DynamoDBModel.LastChangeTimestamp) }, { "#S", nameof(DynamoDBModel.SearchString) } };
            var expressionAttributeValues = new Dictionary<string, DynamoDBEntry>() { { ":dp1", item.DataVersion + 1 }, { ":t", CurrentTimestamp() }, { ":s", item.GetSearchString() } };
            var statements = new List<string>() { "#D=:dp1", "#T=:t", "#S=:s" };
            foreach (string field in fields)
            {
                Type? entryConverter = item.GetDynamoDBConverter(field);
                IPropertyConverter? converter = null;
                if (entryConverter != null)
                {
                    converter = Activator.CreateInstance(entryConverter) as IPropertyConverter;
                }
                string value = (converter == null) ? item[field]?.ToString() ?? "" : converter.ToEntry(item[field]);
                statements.Add($"#{field}=:{field}");
                expressionAttributeValues.Add($":{field}", value);
                expressionAttributeNames.Add($"#{field}", field);
            }

            var updateExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            updateExpression.ExpressionAttributeNames = expressionAttributeNames;
            updateExpression.ExpressionAttributeValues = expressionAttributeValues;
            updateExpression.ExpressionStatement = "set " + String.Join(", ", statements);


            var tw = dBContext.CreateTransactWrite<T>();
            string? hashKey = item.GetHashKeyValue();
            string? rangeKey = item.GetRangeKeyValue();
            if (hashKey == null) throw new Exception("Document is missing a hash key");

            if (rangeKey == null)
                tw.AddSaveItem(hashKey, updateExpression, conditionExpression);
            else
                tw.AddSaveItem(hashKey, rangeKey, updateExpression, conditionExpression);

            try
            {
                await tw.ExecuteAsync();
                await dBContext.LoadAsync(item);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }


        protected async Task<bool> SetDBEntryStatusAtomic<T, S>(T item, S newStatus, List<S> fromStatuses) where T : NoSQLModel, INoSQLStatic, IStateful<S> where S : struct
        {
            var statement = new StringBuilder("#D=:d");
            var enumConvert = new DynamoEnumStringConverter<S>();
            var updateExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            updateExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#S", nameof(IStateful<S>.Status) }, { "#D", "DataVersion" } };
            updateExpression.ExpressionAttributeValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry>() { { ":s", enumConvert.ToEntry(newStatus) }, { ":dp1", item.DataVersion + 1 } };
            updateExpression.ExpressionStatement = "#S=:s, #D=:dp1";

            var conditionExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            conditionExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#St", nameof(IStateful<S>.Status) }, { "#D", "DataVersion" } };
            conditionExpression.ExpressionAttributeValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry>() { { ":d", item.DataVersion } };

            var n = 0;
            foreach (S status in fromStatuses)
            {
                conditionExpression.ExpressionAttributeValues.Add($":s{n}", enumConvert.ToEntry(status));
                statement.Append($"#St=:s{n} OR");
            }
            statement = statement.Remove(statement.Length - 3, 3);


            var tw = dBContext.CreateTransactWrite<T>();
            string? hashKey = item.GetHashKeyValue();
            string? rangeKey = item.GetRangeKeyValue();
            if (hashKey == null) throw new Exception("Document is missing a hash key");

            if (rangeKey == null)
                tw.AddSaveItem(hashKey, updateExpression, conditionExpression);
            else
                tw.AddSaveItem(hashKey, rangeKey, updateExpression, conditionExpression);

            try
            {
                await tw.ExecuteAsync();
                item.Status = newStatus;
                item.DataVersion += 1;
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        #endregion












































        protected async Task<T?> UpdateDBEntry<T>(
            T item,
            List<string> fields,
            bool atomic = true) where T : DynamoDBModel
        {

            var conditionExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            if (atomic)
            {
                conditionExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#D", "DataVersion" } };
                conditionExpression.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>() { { ":d", item.DataVersion } };
                conditionExpression.ExpressionStatement = "#D=:d";
            }

            var expressionAttributeNames = new Dictionary<string, string>() { { "#D", nameof(DynamoDBModel.DataVersion) }, { "#T", nameof(DynamoDBModel.LastChangeTimestamp) }, { "#S", nameof(DynamoDBModel.SearchString) } };
            var expressionAttributeValues = new Dictionary<string, DynamoDBEntry>() { { ":dp1", item.DataVersion + 1 }, { ":t", CurrentTimestamp() }, { ":s", item.GetSearchString() } };
            var statements = new List<string>() { "#D=:dp1", "#T=:t", "#S=:s" };
            foreach (string field in fields)
            {
                Type? entryConverter = item.GetDynamoDBConverter(field);
                IPropertyConverter? converter = null;
                if (entryConverter != null) {
                    converter = Activator.CreateInstance(entryConverter) as IPropertyConverter; 
                }
                string value = (converter == null) ? item[field].ToString() ?? "" : converter.ToEntry(item[field]);
                statements.Add($"#{field}=:{field}");
                expressionAttributeValues.Add($":{field}", value);
                expressionAttributeNames.Add($"#{field}", field);
            }

            var updateExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            updateExpression.ExpressionAttributeNames = expressionAttributeNames;
            updateExpression.ExpressionAttributeValues = expressionAttributeValues;
            updateExpression.ExpressionStatement = "set " + String.Join(", ", statements);


            var tw = dBContext.CreateTransactWrite<T>();
            string? hashKey = item.GetHashKeyValue();
            string? rangeKey = item.GetRangeKeyValue();
            if (hashKey == null) throw new Exception("Document is missing a hash key");

            if (rangeKey == null)
                tw.AddSaveItem(hashKey, updateExpression, conditionExpression);
            else
                tw.AddSaveItem(hashKey, rangeKey, updateExpression, conditionExpression);

            try
            {
                await tw.ExecuteAsync();
                return await dBContext.LoadAsync(item);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        protected async Task<ListResponse<T>> SearchDBEntries<T>(
            string hashKeyProperty, string hashKeyValue, string search, int items, string? previousToken = null, List<string>? attributesToGet = null, bool getTotal = true)
        where T : DynamoDBModel
        {
            Amazon.DynamoDBv2.DocumentModel.Expression filterExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            filterExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#S", nameof(DynamoDBModel.SearchString) } };
            filterExpression.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>() { { ":s", search } };
            filterExpression.ExpressionStatement = $"contains(#S, :s)";

            return await GetDBEntries<T>(hashKeyProperty, hashKeyValue, items, previousToken, attributesToGet: attributesToGet, filterExpression: filterExpression, getTotal: getTotal);
        }

        protected async Task<ListResponse<T>> SearchDBEntries2<T>(
            string hashKeyValue, string search, int items, string? previousToken = null, List<string>? attributesToGet = null, string? index = null)
        where T : DynamoDBModel, INoSQLStatic
        {
            Amazon.DynamoDBv2.DocumentModel.Expression filterExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            filterExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#S", nameof(DynamoDBModel.SearchString) } };
            filterExpression.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>() { { ":s", search } };
            filterExpression.ExpressionStatement = $"contains(#S, :s)";

            return await GetDBEntries2<T>(hashKeyValue, items, previousToken, index, attributesToGet, filterExpression);
        }

        protected async Task<ListResponse<T>> GetDBEntries<T>(
            string hashKeyName, string hashKeyValue, int items, string? previousToken = null, string? index = null, List<string>? attributesToGet = null, Amazon.DynamoDBv2.DocumentModel.Expression? filterExpression = null, bool getTotal = true)
        {

            int totalElements = -1;

            var config = new QueryOperationConfig()
            {
                Limit = 1000,
                KeyExpression = new Amazon.DynamoDBv2.DocumentModel.Expression()
                {
                    ExpressionAttributeNames = new Dictionary<string, string>() { { $"#{hashKeyName}", hashKeyName } },
                    ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>() { { $":{hashKeyName}", hashKeyValue } },
                    ExpressionStatement = $"#{hashKeyName}=:{hashKeyName}"
                },
                FilterExpression = filterExpression,
                BackwardSearch = true
            };

            if (index != null) config.IndexName = index;

            if (getTotal)
            {
                config.Select = SelectValues.Count;
                var table = dBContext.GetTargetTable<T>();
                var countSearch = table.Query(config).GetRemainingAsync();
                totalElements = (await countSearch).Count;
            }
            else{
                config.PaginationToken = previousToken;
            }
            
            if(attributesToGet != null)
            {
                config.AttributesToGet = attributesToGet;
                config.Select = SelectValues.SpecificAttributes;
            }
            else
            {
                config.Select = SelectValues.AllAttributes;
            }

            


            var search = dBContext.FromQueryAsync<T>(config);
            List<T> entries = new List<T>();
            if (items < 0) {
                entries.AddRange(await search.GetRemainingAsync());
            }
            else
            {
                entries.AddRange(await search.GetNextSetAsync());
            }
            

            return new ListResponse<T>() { Entries = entries, NextToken = search.PaginationToken, Total = totalElements };
        }

        protected async Task<ListResponse<T>> GetDBEntriesByGSI<T>(
            string indexName,
            string hashKeyName,
            string hashKeyValue,
            string rangeKeyName,
            string rangeKeyValue,
            int items,
            string? previousToken = null,
            List<string>? attributesToGet = null,
            Amazon.DynamoDBv2.DocumentModel.Expression? filterExpression = null,
            bool getTotal = true)
        where T : DynamoDBModel
        {
            int totalElements = -1;

            var config = new QueryOperationConfig()
            {
                Limit = 1000,
                IndexName = indexName,
                KeyExpression = new Amazon.DynamoDBv2.DocumentModel.Expression()
                {
                    ExpressionAttributeNames = new Dictionary<string, string>()
                    {
                        { $"#{hashKeyName}", hashKeyName },
                        { $"#{rangeKeyName}", rangeKeyName }
                    },
                    ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>()
                    {
                        { $":{hashKeyName}", hashKeyValue },
                        { $":{rangeKeyName}", rangeKeyValue }
                    },
                    ExpressionStatement = $"#{hashKeyName}=:{hashKeyName} AND #{rangeKeyName}=:{rangeKeyName}"
                },
                FilterExpression = filterExpression,
                BackwardSearch = true
            };

            if (getTotal)
            {
                config.Select = SelectValues.Count;
                var table = dBContext.GetTargetTable<T>();
                var countSearch = table.Query(config).GetRemainingAsync();
                totalElements = (await countSearch).Count;
            }
            else
            {
                config.PaginationToken = previousToken;
            }

            if (attributesToGet != null)
            {
                config.AttributesToGet = attributesToGet;
                config.Select = SelectValues.SpecificAttributes;
            }
            else
            {
                config.Select = SelectValues.AllAttributes;
            }

            var search = dBContext.FromQueryAsync<T>(config);
            List<T> entries = new List<T>();
            if (items < 0)
            {
                entries.AddRange(await search.GetRemainingAsync());
            }
            else
            {
                entries.AddRange(await search.GetNextSetAsync());
            }

            return new ListResponse<T>() { Entries = entries, NextToken = search.PaginationToken, Total = totalElements };
        }

        protected async Task<T?> SetDBEntryStatusAtomic<T, S>(T item, S newStatus, List<S> oldStatuses, string statusFieldName = "Status") where T : DynamoDBModel where S : struct, IConvertible
        {
            var statement = new StringBuilder("#D=:d");
            var enumConvert = new DynamoEnumStringConverter<S>();
            var updateExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            updateExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#S", statusFieldName }, { "#D", "DataVersion" } };
            updateExpression.ExpressionAttributeValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry>() { { ":s", enumConvert.ToEntry(newStatus) }, { ":dp1", item.DataVersion+1 } };
            updateExpression.ExpressionStatement = "#S=:s, #D=:dp1";

            var conditionExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            conditionExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#St", statusFieldName }, { "#D", "DataVersion" } };
            conditionExpression.ExpressionAttributeValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry>() { { ":d", item.DataVersion } };
            
            var n = 0;
            foreach (S status in oldStatuses)
            {
                conditionExpression.ExpressionAttributeValues.Add($":s{n}", enumConvert.ToEntry(status));
                statement.Append($"#St=:s{n} OR");
            }
            statement = statement.Remove(statement.Length - 3, 3);
            

            var tw = dBContext.CreateTransactWrite<T>();
            string? hashKey = item.GetHashKeyValue();
            string? rangeKey = item.GetRangeKeyValue();
            if (hashKey == null) throw new Exception("Document is missing a hash key");

            if (rangeKey == null)
                tw.AddSaveItem(hashKey, updateExpression, conditionExpression);
            else
                tw.AddSaveItem(hashKey, rangeKey, updateExpression, conditionExpression);

            try
            {
                await tw.ExecuteAsync();
                return await dBContext.LoadAsync(item);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        protected async Task<T?> SetDBEntryStatusAtomic<T, S>(T item, S newStatus, S? oldStatus = null, string statusFieldName = "Status") where T : DynamoDBModel where S : struct, IConvertible
        {
            var enumConvert = new DynamoEnumStringConverter<S>();
            var updateExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            updateExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#S", statusFieldName }, { "#D", "DataVersion" } };
            updateExpression.ExpressionAttributeValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry>() { { ":s", enumConvert.ToEntry(newStatus) }, { ":dp1", item.DataVersion + 1 } };
            updateExpression.ExpressionStatement = "set #S=:s, #D=:dp1";

            var conditionExpression = new Amazon.DynamoDBv2.DocumentModel.Expression();
            conditionExpression.ExpressionStatement = "#D=:d";
            conditionExpression.ExpressionAttributeNames = new Dictionary<string, string>() { { "#D", "DataVersion" } };
            conditionExpression.ExpressionAttributeValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry>() { { ":d", item.DataVersion } };

            if (oldStatus != null)
            {
                conditionExpression.ExpressionStatement = conditionExpression.ExpressionStatement + " and #St=:st1";
                conditionExpression.ExpressionAttributeNames.Add("#St", statusFieldName);
                conditionExpression.ExpressionAttributeValues.Add(":st1", enumConvert.ToEntry(oldStatus));
            }

            var tw = dBContext.CreateTransactWrite<T>();
            string? hashKey = item.GetHashKeyValue();
            string? rangeKey = item.GetRangeKeyValue();
            if (hashKey == null) throw new Exception("Document is missing a hash key");

            if(rangeKey == null)
                tw.AddSaveItem(hashKey, updateExpression, conditionExpression);
            else
                tw.AddSaveItem(hashKey, rangeKey, updateExpression, conditionExpression);

            try
            {
                await tw.ExecuteAsync();
                return await dBContext.LoadAsync(item);
            } catch (Exception ex) {
                return null;
            }

        }

        protected async Task<T> PutDBEntry<T>(T entity) where T : DynamoDBModel
        {
            try
            {
                entity.DataVersion += 1;
                entity.LastChangeTimestamp = CurrentTimestamp();
                entity.SearchString = entity.GetSearchString();
                await dBContext.SaveAsync(entity);
                return entity;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        

        protected async Task<bool> DeleteDBEntry<T>(string hashKey, bool notFoundIsGood = false) where T : DynamoDBModel
        {
            try
            {
                await dBContext.DeleteAsync<T>(hashKey);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        protected async Task<bool> DeleteDBEntry<T>(string hashKey, string rangeKey, string? indexName = null, bool notFoundIsGood = false) where T : DynamoDBModel
        {
            var config = new DynamoDBOperationConfig();
            if (indexName != null) config.IndexName = indexName;
            try
            {
                await dBContext.DeleteAsync<T>(hashKey, rangeKey, config);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        protected async Task<bool> DeleteDBEntry<T>(T item, bool notFoundIsGood = false) where T : DynamoDBModel
        {
            try
            {
                var hashKeyValue = item.GetHashKeyValue();
                if (string.IsNullOrEmpty(hashKeyValue))
                {
                    return false;
                }

                var rangeKeyValue = item.GetRangeKeyValue();

                if (!string.IsNullOrEmpty(rangeKeyValue))
                {
                    // Model has both hash and range key
                    await dBContext.DeleteAsync<T>(hashKeyValue, rangeKeyValue);
                }
                else
                {
                    // Model has only hash key
                    await dBContext.DeleteAsync<T>(hashKeyValue);
                }

                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        protected async Task<List<T>> BatchGetDBEntry<T>(string hashKey, List<string> rangeKeys) where T : DynamoDBModel
        {
            var br = dBContext.CreateBatchGet<T>();
            rangeKeys.ForEach(e => br.AddKey(hashKey, e));

            try
            {
                await br.ExecuteAsync();
                return br.Results;
            }
            catch (Exception ex)
            {
                return new List<T>();
            }
        }

        protected async Task<List<T>> BatchGetDBEntry<T>(string hashKey, List<string> rangeKeys, string indexName, string rangeKeyName) where T : DynamoDBModel
        {
            var config = new DynamoDBOperationConfig();
            var search = dBContext.QueryAsync<T>(hashKey,
                new DynamoDBOperationConfig()
                {
                    IndexName = indexName,
                    QueryFilter = new List<ScanCondition>() {
                            {
                                new ScanCondition(
                                    rangeKeyName,
                                    ScanOperator.In,
                                    rangeKeys
                                )
                            }
                    }
                }
            );

            try
            {
                var entity = await search.GetRemainingAsync();
                return entity;
            }
            catch (Exception ex)
            {
                return new List<T>();
            }
        }

        protected async Task<T> GetDBEntry<T>(string hashKey, string rangeKey, string indexName, string propertyName) where T : DynamoDBModel
        {
            var config = new DynamoDBOperationConfig();
            var search = dBContext.QueryAsync<T>(hashKey,
                new DynamoDBOperationConfig()
                {
                    IndexName = indexName,
                    QueryFilter = new List<ScanCondition>() {
                            {
                                new ScanCondition(
                                    propertyName,
                                    ScanOperator.Equal,
                                    [rangeKey]
                                )
                            }
                    }
                }
            );

            try
            {
                var entity = await search.GetRemainingAsync();
                if (entity.Count != 1) return null;
                return entity[0];
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        protected async Task<T> GetDBEntry<T>(string hashKey, string rangeKey) where T : DynamoDBModel
        {

            try
            {
                var entity = await dBContext.LoadAsync<T>(hashKey, rangeKey);
                return entity;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        protected async Task<T> GetDBEntry<T>(string hashKey) where T : DynamoDBModel
        {
            try
            {
                var entity = await dBContext.LoadAsync<T>(hashKey);
                return entity;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        #region DB Join Operations

        /// <summary>
        /// Represents a join condition between two tables
        /// </summary>
        public class JoinCondition
        {
            public string FirstTableField { get; set; } = string.Empty;
            public string SecondTableField { get; set; } = string.Empty;
        }

        /// <summary>
        /// Configuration for table queries in join operations
        /// </summary>
        public class TableQueryConfig
        {
            public string HashKeyValue { get; set; } = string.Empty;
            public string? Index { get; set; }
            public List<string>? AttributesToGet { get; set; }
            public Expression? AdditionalFilterExpression { get; set; }
        }

        /// <summary>
        /// Configuration for DB join operations
        /// </summary>
        public class DBJoinConfig<T1, T2>
            where T1 : INoSQLModel, INoSQLStatic
            where T2 : INoSQLModel, INoSQLStatic
        {
            public TableQueryConfig FirstTable { get; set; } = new TableQueryConfig();
            public TableQueryConfig SecondTable { get; set; } = new TableQueryConfig();
            public JoinCondition JoinCondition { get; set; } = new JoinCondition();
            public int TargetResultCount { get; set; } = 10;
        }

        /// <summary>
        /// Represents a combined pagination token for join operations
        /// </summary>
        public class JoinPaginationToken
        {
            public string? FirstTableToken { get; set; }
            public string? SecondTableToken { get; set; }
            public int FirstTableProcessedCount { get; set; } = 0;

            public static string? Encode(JoinPaginationToken? token)
            {
                if (token == null) return null;
                var json = JsonSerializer.Serialize(token);
                return Convert.ToBase64String(Encoding.UTF8.GetBytes(json));
            }

            public static JoinPaginationToken? Decode(string? encodedToken)
            {
                if (string.IsNullOrEmpty(encodedToken)) return null;
                try
                {
                    var json = Encoding.UTF8.GetString(Convert.FromBase64String(encodedToken));
                    return JsonSerializer.Deserialize<JoinPaginationToken>(json);
                }
                catch
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Result of a join operation containing entries from both tables
        /// </summary>
        public class JoinResult<T1, T2>
            where T1 : INoSQLModel, INoSQLStatic
            where T2 : INoSQLModel, INoSQLStatic
        {
            public List<(T1 First, T2 Second)> Entries { get; set; } = new List<(T1, T2)>();
            public string? NextToken { get; set; }
            public int Total { get; set; } = -1;
        }

        /// <summary>
        /// Performs a virtual join between two DynamoDB tables by querying them sequentially
        /// and matching results based on the specified join condition.
        /// </summary>
        /// <typeparam name="T1">Type of the first table model</typeparam>
        /// <typeparam name="T2">Type of the second table model</typeparam>
        /// <param name="config">Configuration for the join operation</param>
        /// <param name="previousToken">Previous pagination token for continuing a paginated query</param>
        /// <returns>Join result containing matched entries from both tables</returns>
        protected async Task<JoinResult<T1, T2>> GetDBJoin<T1, T2>(
            DBJoinConfig<T1, T2> config,
            string? previousToken = null)
            where T1 : INoSQLModel, INoSQLStatic
            where T2 : INoSQLModel, INoSQLStatic
        {
            var result = new JoinResult<T1, T2>();
            var paginationToken = JoinPaginationToken.Decode(previousToken);

            var firstTableEntries = new List<T1>();
            var currentFirstTableToken = paginationToken?.FirstTableToken;
            var processedFromFirstTable = paginationToken?.FirstTableProcessedCount ?? 0;

            // Continue iterating until we have enough results or no more data
            while (result.Entries.Count < config.TargetResultCount)
            {
                // Step 1: Query the first table if we need more entries
                if (firstTableEntries.Count == 0)
                {
                    var firstTableResponse = await GetDBEntries2<T1>(
                        config.FirstTable.HashKeyValue,
                        Math.Max(config.TargetResultCount * 2, 50), // Get more entries to account for filtering
                        currentFirstTableToken,
                        config.FirstTable.Index,
                        config.FirstTable.AttributesToGet,
                        config.FirstTable.AdditionalFilterExpression
                    );

                    if (firstTableResponse.Entries.Count == 0)
                    {
                        // No more entries in first table
                        break;
                    }

                    firstTableEntries.AddRange(firstTableResponse.Entries);
                    currentFirstTableToken = firstTableResponse.NextToken;

                    // Set total only on first iteration
                    if (paginationToken == null && result.Total == -1)
                    {
                        result.Total = firstTableResponse.Total;
                    }
                }

                // Step 2: Extract join values from first table entries
                var joinValues = new List<string>();
                var firstTableEntriesForJoin = new List<T1>();

                for (int i = processedFromFirstTable; i < firstTableEntries.Count && joinValues.Count < config.TargetResultCount; i++)
                {
                    var entry = firstTableEntries[i];
                    var joinValue = GetPropertyValue(entry, config.JoinCondition.FirstTableField);
                    if (!string.IsNullOrEmpty(joinValue))
                    {
                        joinValues.Add(joinValue);
                        firstTableEntriesForJoin.Add(entry);
                    }
                }

                if (joinValues.Count == 0)
                {
                    // No valid join values found, move to next batch
                    processedFromFirstTable = firstTableEntries.Count;
                    firstTableEntries.Clear();
                    continue;
                }

                // Step 3: Build expression for second table (KeyExpression or FilterExpression)
                Expression? filterExpression = config.SecondTable.AdditionalFilterExpression;
                Expression? additionalKeyExpression = null;

                // Check if join condition is on a Range Key
                if (IsRangeKey<T2>(config.JoinCondition.SecondTableField, config.SecondTable.Index))
                {
                    // Use KeyExpression for Range Key joins
                    additionalKeyExpression = BuildInKeyExpression(config.JoinCondition.SecondTableField, joinValues);
                    // Keep the original filter expression as-is
                }
                else
                {
                    // Use FilterExpression for non-key field joins
                    var joinFilterExpression = BuildInFilterExpression(config.JoinCondition.SecondTableField, joinValues);

                    // Combine with additional filter if provided
                    if (config.SecondTable.AdditionalFilterExpression != null)
                    {
                        filterExpression = CombineFilterExpressions(joinFilterExpression, config.SecondTable.AdditionalFilterExpression);
                    }
                    else
                    {
                        filterExpression = joinFilterExpression;
                    }
                }

                // Step 4: Query second table
                var secondTableResponse = await GetDBEntries2<T2>(
                    config.SecondTable.HashKeyValue,
                    firstTableEntries.Count, // Get all matching entries
                    paginationToken?.SecondTableToken,
                    config.SecondTable.Index,
                    config.SecondTable.AttributesToGet,
                    filterExpression,
                    skipCount: true,
                    additionalKeyExpression
                );

                // Step 5: Match entries and add to result
                foreach (var secondEntry in secondTableResponse.Entries)
                {
                    var secondJoinValue = GetPropertyValue(secondEntry, config.JoinCondition.SecondTableField);
                    var matchingFirstEntry = firstTableEntriesForJoin.FirstOrDefault(f =>
                        GetPropertyValue(f, config.JoinCondition.FirstTableField) == secondJoinValue);

                    if (matchingFirstEntry != null)
                    {
                        result.Entries.Add((matchingFirstEntry, secondEntry));

                        if (result.Entries.Count >= config.TargetResultCount)
                        {
                            break;
                        }
                    }
                }

                // Update processed count
                processedFromFirstTable += firstTableEntriesForJoin.Count;

                // Check if we need to continue or if we're done
                if (result.Entries.Count >= config.TargetResultCount ||
                    (processedFromFirstTable >= firstTableEntries.Count && currentFirstTableToken == null))
                {
                    break;
                }

                // If we've processed all current first table entries but have more, clear and continue
                if (processedFromFirstTable >= firstTableEntries.Count)
                {
                    firstTableEntries.Clear();
                    processedFromFirstTable = 0;
                }
            }

            // Set next token if there's more data
            if (currentFirstTableToken != null || processedFromFirstTable < firstTableEntries.Count)
            {
                var nextPaginationToken = new JoinPaginationToken
                {
                    FirstTableToken = processedFromFirstTable >= firstTableEntries.Count ? currentFirstTableToken : null,
                    SecondTableToken = null, // Reset for next iteration
                    FirstTableProcessedCount = processedFromFirstTable >= firstTableEntries.Count ? 0 : processedFromFirstTable
                };
                result.NextToken = JoinPaginationToken.Encode(nextPaginationToken);
            }

            return result;
        }

        /// <summary>
        /// Gets the value of a property from an object using reflection
        /// </summary>
        /// <param name="obj">The object to get the property value from</param>
        /// <param name="propertyName">The name of the property</param>
        /// <returns>The property value as a string, or null if not found</returns>
        private string? GetPropertyValue(object obj, string propertyName)
        {
            var property = obj.GetType().GetProperty(propertyName);
            var value = property?.GetValue(obj);
            return value?.ToString();
        }

        /// <summary>
        /// Checks if a field is a Range Key for the given table type and index
        /// </summary>
        /// <typeparam name="T">The table model type</typeparam>
        /// <param name="fieldName">The field name to check</param>
        /// <param name="index">Optional index name</param>
        /// <returns>True if the field is a Range Key, false otherwise</returns>
        private bool IsRangeKey<T>(string fieldName, string? index = null) where T : INoSQLStatic
        {
            var rangeKeyPropertyName = T.GetRangeKeyPropertyName(index);
            return !string.IsNullOrEmpty(rangeKeyPropertyName) && rangeKeyPropertyName.Equals(fieldName, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Builds a key expression for IN operations on range keys (rangeKey IN (value1, value2, ...))
        /// Note: DynamoDB doesn't support IN operations on key expressions, so this creates OR conditions
        /// </summary>
        /// <param name="fieldName">The range key field name</param>
        /// <param name="values">The values to match against</param>
        /// <returns>A DynamoDB key expression with OR conditions</returns>
        private Expression BuildInKeyExpression(string fieldName, List<string> values)
        {
            var expression = new Expression();
            expression.ExpressionAttributeNames = new Dictionary<string, string>
            {
                { $"#{fieldName}", fieldName }
            };
            expression.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>();

            var statements = new List<string>();
            for (int i = 0; i < values.Count; i++)
            {
                var valueKey = $":{fieldName}{i}";
                expression.ExpressionAttributeValues[valueKey] = values[i];
                statements.Add($"#{fieldName}={valueKey}");
            }

            expression.ExpressionStatement = string.Join(" OR ", statements);
            return expression;
        }

        /// <summary>
        /// Builds a filter expression for IN operations (field IN (value1, value2, ...))
        /// </summary>
        /// <param name="fieldName">The field name to filter on</param>
        /// <param name="values">The values to match against</param>
        /// <returns>A DynamoDB filter expression</returns>
        private Expression BuildInFilterExpression(string fieldName, List<string> values)
        {
            var expression = new Expression();
            expression.ExpressionAttributeNames = new Dictionary<string, string>
            {
                { $"#{fieldName}", fieldName }
            };
            expression.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>();

            var statements = new List<string>();
            for (int i = 0; i < values.Count; i++)
            {
                var valueKey = $":{fieldName}{i}";
                expression.ExpressionAttributeValues[valueKey] = values[i];
                statements.Add($"#{fieldName}={valueKey}");
            }

            expression.ExpressionStatement = string.Join(" OR ", statements);
            return expression;
        }

        /// <summary>
        /// Combines two key expressions with AND logic
        /// </summary>
        /// <param name="expr1">First key expression (typically hash key)</param>
        /// <param name="expr2">Second key expression (typically range key or additional condition)</param>
        /// <returns>Combined key expression</returns>
        private Expression CombineKeyExpressions(Expression expr1, Expression expr2)
        {
            var combined = new Expression();

            // Combine attribute names
            combined.ExpressionAttributeNames = new Dictionary<string, string>();
            if (expr1.ExpressionAttributeNames != null)
            {
                foreach (var kvp in expr1.ExpressionAttributeNames)
                {
                    combined.ExpressionAttributeNames[kvp.Key] = kvp.Value;
                }
            }
            if (expr2.ExpressionAttributeNames != null)
            {
                foreach (var kvp in expr2.ExpressionAttributeNames)
                {
                    combined.ExpressionAttributeNames[kvp.Key] = kvp.Value;
                }
            }

            // Combine attribute values
            combined.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>();
            if (expr1.ExpressionAttributeValues != null)
            {
                foreach (var kvp in expr1.ExpressionAttributeValues)
                {
                    combined.ExpressionAttributeValues[kvp.Key] = kvp.Value;
                }
            }
            if (expr2.ExpressionAttributeValues != null)
            {
                foreach (var kvp in expr2.ExpressionAttributeValues)
                {
                    combined.ExpressionAttributeValues[kvp.Key] = kvp.Value;
                }
            }

            // Combine statements with AND
            combined.ExpressionStatement = $"({expr1.ExpressionStatement}) AND ({expr2.ExpressionStatement})";

            return combined;
        }

        /// <summary>
        /// Combines two filter expressions with AND logic
        /// </summary>
        /// <param name="expr1">First expression</param>
        /// <param name="expr2">Second expression</param>
        /// <returns>Combined expression</returns>
        private Expression CombineFilterExpressions(Expression expr1, Expression expr2)
        {
            var combined = new Expression();

            // Combine attribute names
            combined.ExpressionAttributeNames = new Dictionary<string, string>();
            if (expr1.ExpressionAttributeNames != null)
            {
                foreach (var kvp in expr1.ExpressionAttributeNames)
                {
                    combined.ExpressionAttributeNames[kvp.Key] = kvp.Value;
                }
            }
            if (expr2.ExpressionAttributeNames != null)
            {
                foreach (var kvp in expr2.ExpressionAttributeNames)
                {
                    combined.ExpressionAttributeNames[kvp.Key] = kvp.Value;
                }
            }

            // Combine attribute values
            combined.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>();
            if (expr1.ExpressionAttributeValues != null)
            {
                foreach (var kvp in expr1.ExpressionAttributeValues)
                {
                    combined.ExpressionAttributeValues[kvp.Key] = kvp.Value;
                }
            }
            if (expr2.ExpressionAttributeValues != null)
            {
                foreach (var kvp in expr2.ExpressionAttributeValues)
                {
                    combined.ExpressionAttributeValues[kvp.Key] = kvp.Value;
                }
            }

            // Combine statements with AND
            combined.ExpressionStatement = $"({expr1.ExpressionStatement}) AND ({expr2.ExpressionStatement})";

            return combined;
        }

        /// <summary>
        /// Configuration for simple query operations with additional filters
        /// </summary>
        public class QueryConfig<T> where T : INoSQLModel, INoSQLStatic
        {
            public string HashKeyValue { get; set; } = string.Empty;
            public string? Index { get; set; }
            public List<string>? AttributesToGet { get; set; }
            public Dictionary<string, object> FilterConditions { get; set; } = new Dictionary<string, object>();
            public int Limit { get; set; } = 10;
        }

        /// <summary>
        /// Performs a query on a single DynamoDB table with additional filter conditions.
        /// This is a simpler alternative to GetDBJoin for single table operations with constant filters.
        /// </summary>
        /// <typeparam name="T">Type of the table model</typeparam>
        /// <param name="config">Configuration for the query operation</param>
        /// <param name="previousToken">Previous pagination token for continuing a paginated query</param>
        /// <returns>List response containing the query results</returns>
        protected async Task<ListResponse<T>> Query<T>(
            QueryConfig<T> config,
            string? previousToken = null)
            where T : INoSQLModel, INoSQLStatic
        {
            Expression? filterExpression = null;

            // Build filter expression from filter conditions
            if (config.FilterConditions.Count > 0)
            {
                filterExpression = BuildFilterExpression(config.FilterConditions);
            }

            // Use the existing GetDBEntries2 method
            return await GetDBEntries2<T>(
                config.HashKeyValue,
                config.Limit,
                previousToken,
                config.Index,
                config.AttributesToGet,
                filterExpression
            );
        }

        /// <summary>
        /// Overload for Query method with individual parameters for simpler usage
        /// </summary>
        /// <typeparam name="T">Type of the table model</typeparam>
        /// <param name="hashKeyValue">Hash key value to query</param>
        /// <param name="filterConditions">Dictionary of field names and values to filter on</param>
        /// <param name="limit">Maximum number of items to return</param>
        /// <param name="previousToken">Previous pagination token</param>
        /// <param name="index">Optional index name</param>
        /// <param name="attributesToGet">Optional list of attributes to retrieve</param>
        /// <returns>List response containing the query results</returns>
        protected async Task<ListResponse<T>> Query<T>(
            string hashKeyValue,
            Dictionary<string, object> filterConditions,
            int limit = 10,
            string? previousToken = null,
            string? index = null,
            List<string>? attributesToGet = null)
            where T : INoSQLModel, INoSQLStatic
        {
            var config = new QueryConfig<T>
            {
                HashKeyValue = hashKeyValue,
                FilterConditions = filterConditions,
                Limit = limit,
                Index = index,
                AttributesToGet = attributesToGet
            };

            return await Query(config, previousToken);
        }

        /// <summary>
        /// Builds a filter expression from a dictionary of field conditions
        /// </summary>
        /// <param name="filterConditions">Dictionary of field names and values</param>
        /// <returns>A DynamoDB filter expression</returns>
        private Expression BuildFilterExpression(Dictionary<string, object> filterConditions)
        {
            var expression = new Expression();
            expression.ExpressionAttributeNames = new Dictionary<string, string>();
            expression.ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>();

            var statements = new List<string>();
            int valueIndex = 0;

            foreach (var condition in filterConditions)
            {
                var fieldName = condition.Key;
                var value = condition.Value;

                var nameKey = $"#{fieldName}";
                var valueKey = $":val{valueIndex}";

                expression.ExpressionAttributeNames[nameKey] = fieldName;
                expression.ExpressionAttributeValues[valueKey] = value.ToString();

                statements.Add($"{nameKey}={valueKey}");
                valueIndex++;
            }

            expression.ExpressionStatement = string.Join(" AND ", statements);
            return expression;
        }

        #endregion

        protected string? GetClaimValue(string claimName)
        {
            return HttpContext.User.FindFirstValue(claimName);
        }

        protected List<Claim> GetClaims()
        {
            return HttpContext.User.Claims.ToList();
        }

        protected string GetAccountId()
        {
            string? accountId = GetClaimValue("AccountId");
            if (accountId == null) return string.Empty;
            return accountId;
        }

        protected string GetControllerRoute()
        {
            return RouteData.Values["controller"] as string ?? string.Empty;
        }

        protected long CurrentTimestamp()
        {
            return (long)(DateTime.UtcNow - new DateTime(1970, 1, 1)).TotalSeconds;
        }

        protected async Task<bool> DispatchApiEvent(object payload, MicroserviceType microserviceTarget, string controller, string endpoint, int delayInSeconds = 0)
        {
            return await Task.FromResult(true);
        }



    }
}
